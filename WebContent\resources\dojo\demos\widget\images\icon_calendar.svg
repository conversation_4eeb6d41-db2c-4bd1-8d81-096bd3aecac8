<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 12.0.1, SVG Export Plug-In . SVG Version: 6.00 Build 51448)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" [
	<!ENTITY ns_svg "http://www.w3.org/2000/svg">
	<!ENTITY ns_xlink "http://www.w3.org/1999/xlink">
]>
<svg  version="1.1" id="Layer_1" xmlns="&ns_svg;" xmlns:xlink="&ns_xlink;" width="176.045" height="168.232"
	 viewBox="0 0 176.045 168.232" overflow="visible" enable-background="new 0 0 176.045 168.232" xml:space="preserve">
<linearGradient id="XMLID_9_" gradientUnits="userSpaceOnUse" x1="183.1885" y1="13.0562" x2="7.0782" y2="196.418">
	<stop  offset="0" style="stop-color:#FFFFFF"/>
	<stop  offset="1" style="stop-color:#000000"/>
</linearGradient>
<polygon fill="url(#XMLID_9_)" points="176.045,159 17.045,168 30.045,34 171.045,0 "/>
<linearGradient id="XMLID_10_" gradientUnits="userSpaceOnUse" x1="199.2114" y1="90.3262" x2="-11.7361" y2="79.1703">
	<stop  offset="0" style="stop-color:#FFFFFF"/>
	<stop  offset="1" style="stop-color:#000000"/>
</linearGradient>
<polygon fill="url(#XMLID_10_)" points="172.045,154 13.045,167 25.649,30.583 165.045,2 "/>
<g>
	<path fill="#FFFFFF" d="M14.367,168.232L26.133,31.29L160.419,4.138c0,0,8.274,116.749,7.146,124.641
		c-0.282,5.638-5.818,11.074-12.583,14.175"/>
	<g>
		<linearGradient id="XMLID_11_" gradientUnits="userSpaceOnUse" x1="33.6616" y1="70.7783" x2="50.4707" y2="70.7783">
			<stop  offset="0" style="stop-color:#FFFFFF"/>
			<stop  offset="1" style="stop-color:#999999"/>
		</linearGradient>
		<path fill="url(#XMLID_11_)" stroke="#666666" d="M39.486,69.772c-0.237,3.375-3.204,6.563-6.614,7.106
			c-3.41,0.544-5.962-1.735-5.689-5.08c0.273-3.345,3.24-6.533,6.614-7.106C37.171,64.118,39.724,66.397,39.486,69.772z
			 M33.931,62.93c-4.343,0.745-8.168,4.855-8.526,9.161c-0.359,4.307,2.932,7.244,7.334,6.55c4.403-0.695,8.228-4.806,8.527-9.162
			C41.564,65.123,38.274,62.185,33.931,62.93z"/>
	</g>
	<linearGradient id="XMLID_12_" gradientUnits="userSpaceOnUse" x1="97.2246" y1="-40.4966" x2="97.2246" y2="51.7634">
		<stop  offset="0" style="stop-color:#FFFFFF"/>
		<stop  offset="1" style="stop-color:#336600"/>
	</linearGradient>
	<polygon fill="url(#XMLID_12_)" points="144.35,32.88 50.1,49.908 51.562,26.148 143.848,7.489 	"/>
	<g>
		<path fill="#FFFFFF" d="M109.943,29.293c-0.047-1.848-0.092-4.085-0.072-6.335l-0.064,0.012c-0.466,2.061-1.085,4.379-1.661,6.297
			l-1.828,6.357l-2.564,0.473l-1.459-5.652c-0.443-1.737-0.9-3.834-1.208-5.777l-0.043,0.008c-0.119,2.093-0.223,4.479-0.362,6.416
			l-0.356,5.887l-3.039,0.559L98.5,22.017l4.317-0.852l1.333,4.868c0.424,1.675,0.849,3.534,1.147,5.281l0.065-0.012
			c0.429-1.862,0.947-3.994,1.437-5.803l1.589-5.434l4.233-0.835l0.696,15.359l-3.21,0.59L109.943,29.293z"/>
		<path fill="#FFFFFF" d="M119.161,29.515l-1.038,4.188l-3.404,0.626l4.453-16.394l4.254-0.839l4.545,14.795l-3.534,0.65
			l-1.131-3.804L119.161,29.515z M122.828,26.186l-0.905-3.141c-0.258-0.877-0.515-1.985-0.729-2.87l-0.042,0.008
			c-0.213,0.968-0.426,2.189-0.661,3.137l-0.856,3.47L122.828,26.186z"/>
		<path fill="#FFFFFF" d="M131.225,31.293l-0.059-6.433l-4.615-8.381l3.701-0.731l1.504,3.488c0.459,1.056,0.767,1.838,1.141,2.794
			l0.042-0.008c0.311-1.042,0.642-2.018,1.057-3.221l1.403-4.068l3.637-0.718l-4.613,10.112l0.077,6.563L131.225,31.293z"/>
	</g>
	<linearGradient id="XMLID_13_" gradientUnits="userSpaceOnUse" x1="21.6914" y1="70.1606" x2="21.6914" y2="97.4842">
		<stop  offset="0" style="stop-color:#FFFFFF"/>
		<stop  offset="1" style="stop-color:#000000"/>
	</linearGradient>
	<path fill="url(#XMLID_13_)" stroke="#666666" d="M37.708,70.065c-0.274,3.816-4.302,7.513-8.997,8.258L13.5,80.737
		c-4.695,0.745-8.192-1.672-7.812-5.4l0,0c0.381-3.728,4.449-7.391,9.088-8.183l15.029-2.565
		C34.444,63.797,37.982,66.249,37.708,70.065L37.708,70.065z"/>
	<g>
		<linearGradient id="XMLID_14_" gradientUnits="userSpaceOnUse" x1="29.7603" y1="122.3936" x2="47.3227" y2="122.3936">
			<stop  offset="0" style="stop-color:#FFFFFF"/>
			<stop  offset="1" style="stop-color:#999999"/>
		</linearGradient>
		<path fill="url(#XMLID_14_)" stroke="#666666" d="M35.848,121.617c-0.237,3.374-3.328,6.458-6.891,6.875
			c-3.563,0.417-6.24-1.966-5.967-5.311s3.364-6.43,6.891-6.876C33.408,115.859,36.084,118.241,35.848,121.617z M30.015,114.544
			c-4.54,0.581-8.525,4.558-8.884,8.864c-0.359,4.306,3.092,7.377,7.692,6.846c4.6-0.531,8.585-4.508,8.884-8.863
			C38.005,117.034,34.555,113.963,30.015,114.544z"/>
	</g>
	<linearGradient id="XMLID_15_" gradientUnits="userSpaceOnUse" x1="17.2515" y1="121.4434" x2="17.2515" y2="147.6367">
		<stop  offset="0" style="stop-color:#FFFFFF"/>
		<stop  offset="1" style="stop-color:#000000"/>
	</linearGradient>
	<path fill="url(#XMLID_15_)" stroke="#666666" d="M33.989,121.843c-0.274,3.815-4.472,7.371-9.377,7.941l-15.893,1.849
		c-4.905,0.57-8.572-1.989-8.192-5.716l0,0c0.381-3.729,4.62-7.25,9.468-7.867l15.711-1.999
		C30.555,115.434,34.263,118.026,33.989,121.843L33.989,121.843z"/>
	<g>
		<path fill="#666666" d="M57.347,119.249c2.871,1.119,9.513,3.057,16.278,2.257c8.637-1.021,13.121-5.617,13.263-10.873
			c0.185-6.867-6.622-9.063-13.826-8.104l-6.673,0.889l0.512-11.53l6.288-0.896c5.442-0.882,12.418-3.903,12.586-9.799
			c0.119-4.177-3.158-6.765-9.889-5.723c-5.573,0.863-11.587,4.208-14.51,6.325l-2.667-11.068
			c4.206-3.289,12.443-7.164,21.193-8.612c14.47-2.395,22.364,4.189,22.22,13.833c-0.111,7.478-4.36,13.913-12.976,18.121
			l-0.005,0.221c8.318,0.359,14.987,5.839,14.867,15.065c-0.161,12.479-11.43,22.805-29.64,24.782
			c-9.283,1.008-16.998-0.453-21.049-2.512L57.347,119.249z"/>
		<path fill="#666666" d="M124.392,70.82l-0.215,0.033l-13.12,8.429L108.52,67.27l18.012-11.66l13.124-2.16l1.135,72.252
			l-16.21,1.774L124.392,70.82z"/>
	</g>
</g>
<linearGradient id="XMLID_16_" gradientUnits="userSpaceOnUse" x1="90.7422" y1="115" x2="90.7422" y2="192">
	<stop  offset="0" style="stop-color:#FFFFFF"/>
	<stop  offset="1" style="stop-color:#000000"/>
</linearGradient>
<path fill="url(#XMLID_16_)" d="M167.135,127.889c-0.491,19.555-26.833,19.585-39.021,22.022
	c-25.653,5.131-113.765,18.266-113.765,18.266c14.872-3.077,122.597-32.916,128.975-35.073
	C159.199,149.469,166.943,136.199,167.135,127.889z"/>
</svg>
