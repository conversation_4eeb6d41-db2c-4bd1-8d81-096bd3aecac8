/*
 *   Mango - Open Source M2M - http://mango.serotoninsoftware.com
 *   Copyright (C) 2010 A<PERSON>\u00f6se
 *   <AUTHOR>
 *
 *   This program is free software: you can redistribute it and/or modify
 *   it under the terms of the GNU General Public License as published by
 *   the Free Software Foundation, either version 3 of the License, or
 *   (at your option) any later version.
 *
 *   This program is distributed in the hope that it will be useful,
 *   but WITHOUT ANY WARRANTY; without even the implied warranty of
 *   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *   GNU General Public License for more details.
 *
 *   You should have received a copy of the GNU General Public License
 *   along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */
package com.serotonin.mango.vo.dataSource.mbus;

import net.sf.mbus4j.MBusAddressing;

public abstract class MBusSearchByAddressing {

    private int maxTries;

    /**
     * @return the maxTries
     */
    public int getMaxTries() {
        return maxTries;
    }

    /**
     * @param maxTries the maxTries to set
     */
    public void setMaxTries(int maxTries) {
        this.maxTries = maxTries;
    }

    public abstract MBusAddressing getAddressing();
}
