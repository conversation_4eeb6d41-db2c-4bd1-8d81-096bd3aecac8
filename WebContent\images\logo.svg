<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->

<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   width="210mm"
   height="297mm"
   viewBox="0 0 744.09448819 1052.3622047"
   id="svg2"
   version="1.1"
   inkscape:version="0.91 r13725"
   sodipodi:docname="logo.svg">
  <defs
     id="defs4">
    <clipPath
       id="clipPath3354"
       clipPathUnits="userSpaceOnUse">
      <path
         inkscape:connector-curvature="0"
         id="path3356"
         d="m 0,566.93 566.93,0 L 566.93,0 0,0 0,566.93 Z" />
    </clipPath>
    <filter
       style="color-interpolation-filters:sRGB;"
       inkscape:label="Drop Shadow"
       id="filter3580">
      <feFlood
         flood-opacity="0.498039"
         flood-color="rgb(0,0,0)"
         result="flood"
         id="feFlood3582" />
      <feComposite
         in="flood"
         in2="SourceGraphic"
         operator="in"
         result="composite1"
         id="feComposite3584" />
      <feGaussianBlur
         in="composite1"
         stdDeviation="3"
         result="blur"
         id="feGaussianBlur3586" />
      <feOffset
         dx="3"
         dy="3"
         result="offset"
         id="feOffset3588" />
      <feComposite
         in="SourceGraphic"
         in2="offset"
         operator="over"
         result="composite2"
         id="feComposite3590" />
    </filter>
  </defs>
  <sodipodi:namedview
     id="base"
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1.0"
     inkscape:pageopacity="0.0"
     inkscape:pageshadow="2"
     inkscape:zoom="1.4"
     inkscape:cx="471.95311"
     inkscape:cy="422.60725"
     inkscape:document-units="px"
     inkscape:current-layer="layer1"
     showgrid="false"
     showguides="true"
     inkscape:guide-bbox="true"
     inkscape:window-width="1366"
     inkscape:window-height="746"
     inkscape:window-x="-8"
     inkscape:window-y="-8"
     inkscape:window-maximized="1">
    <sodipodi:guide
       position="693.57143,513.57144"
       orientation="1,0"
       id="guide3452" />
  </sodipodi:namedview>
  <metadata
     id="metadata7">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title></dc:title>
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <g
     inkscape:label="Layer 1"
     inkscape:groupmode="layer"
     id="layer1">
    <text
       style="font-variant:normal;font-weight:600;font-stretch:normal;font-size:25px;font-family:'Myriad Pro Light';-inkscape-font-specification:MyriadPro-Semibold;writing-mode:lr-tb;fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
       id="text3386"
       x="387.58588"
       y="206.5979">
      <tspan
         x="387.58588 408.36087 421.06088 435.36087 449.58588 462.28586 468.71088 473.88586 488.41089 501.31088 506.48587 513.08588 527.61084 540.51086 554.68585 563.46088 569.8609 584.38586 597.08588 611.6109 624.51086 628.78589 643.26086 649.66089 660.08588 674.31085 687.01086"
         y="206.5979"
         sodipodi:role="line"
         id="tspan3388">Manual de Identidade Visual</tspan>
    </text>
    <g
       id="g3526"
       style="filter:url(#filter3580)"
       inkscape:export-xdpi="90"
       inkscape:export-ydpi="90">
      <g
         style="fill:#f8f8f8;fill-opacity:1"
         transform="matrix(1.25,0,0,-1.25,441.60294,530.98046)"
         id="g3358">
        <path
           inkscape:connector-curvature="0"
           id="path3360"
           style="fill:#f8f8f8;fill-opacity:1;fill-rule:nonzero;stroke:none"
           d="m 0,0 -0.049,12.308 c 0,0 -4.014,0.141 -5.362,0.141 -1.736,0 -3.276,-0.056 -4.551,-0.349 -1.276,-0.294 -1.944,-0.631 -2.559,-1.012 -0.868,-0.535 -1.477,-1.227 -1.874,-2.03 -0.394,-0.804 -0.592,-1.73 -0.592,-2.775 0,-1.787 0.415,-3.342 1.704,-4.537 C -11.781,0.354 -9.8,0.105 -9.8,0.105 -7.562,-0.453 0,0 0,0 m 3.353,-4.633 c -6.511,0 -12.587,-0.523 -17.007,0.783 -3.197,0.946 -5.419,2.934 -6.487,4.907 -0.939,1.735 -1.212,3.256 -1.212,4.915 0,1.938 0.426,3.71 1.187,5.051 2.157,3.811 5.829,4.569 8.18,5.085 1.218,0.268 4.511,0.656 7.191,0.656 l 4.746,0 0,3.01 c 0,1.327 -0.139,2.501 -0.421,3.522 -0.281,1.02 -0.733,1.868 -1.358,2.546 -0.626,0.676 -1.436,1.185 -2.431,1.531 -0.996,0.344 -2.22,0.517 -3.675,0.517 -1.556,0 -2.954,-0.187 -4.191,-0.556 -1.238,-0.37 -2.322,-0.779 -3.254,-1.225 -0.932,-0.447 -1.71,-0.855 -2.334,-1.225 -0.626,-0.37 -1.092,-0.554 -1.397,-0.554 -0.205,0 -0.384,0.05 -0.536,0.152 -0.154,0.102 -0.288,0.255 -0.404,0.459 -0.113,0.205 -0.196,0.465 -0.247,0.786 -0.052,0.318 -0.077,0.669 -0.077,1.052 0,0.637 0.045,1.142 0.135,1.512 0.089,0.37 0.305,0.721 0.65,1.053 0.345,0.332 0.938,0.72 1.779,1.168 0.843,0.445 1.812,0.853 2.91,1.224 1.097,0.369 2.296,0.676 3.598,0.918 1.302,0.242 2.615,0.365 3.943,0.365 2.474,0 4.58,-0.282 6.316,-0.843 1.734,-0.561 3.139,-1.384 4.21,-2.469 1.071,-1.085 1.85,-2.431 2.335,-4.037 0.484,-1.608 0.727,-3.484 0.727,-5.628 l 0,-22.712 c 0,-0.679 -0.237,-1.1 -0.56,-1.421 C 4.973,-4.783 3.998,-4.633 3.353,-4.633" />
      </g>
      <g
         style="fill:#f8f8f8;fill-opacity:1"
         transform="matrix(1.25,0,0,-1.25,371.86419,523.3132)"
         id="g3362">
        <path
           inkscape:connector-curvature="0"
           id="path3364"
           style="fill:#f8f8f8;fill-opacity:1;fill-rule:nonzero;stroke:none"
           d="m 0,0 c 0,-1.724 -0.345,-3.261 -1.031,-4.61 -0.688,-1.352 -1.624,-2.487 -2.812,-3.407 -1.187,-0.921 -2.608,-1.636 -4.261,-2.148 -1.655,-0.512 -3.448,-0.769 -5.38,-0.769 -1.189,0 -2.335,0.105 -3.442,0.315 -1.105,0.21 -2.101,0.472 -2.986,0.786 -0.885,0.314 -1.636,0.635 -2.253,0.96 -0.617,0.325 -1.019,0.582 -1.205,0.769 -0.187,0.185 -0.32,0.343 -0.402,0.471 -0.082,0.128 -0.152,0.285 -0.209,0.472 -0.059,0.186 -0.1,0.402 -0.123,0.646 -0.024,0.244 -0.036,0.553 -0.036,0.926 0,0.465 0.012,0.867 0.036,1.205 0.023,0.337 0.075,0.623 0.158,0.856 0.081,0.232 0.185,0.402 0.314,0.507 0.127,0.104 0.285,0.157 0.471,0.157 0.257,0 0.646,-0.169 1.171,-0.506 0.523,-0.339 1.193,-0.711 2.009,-1.118 0.814,-0.408 1.787,-0.781 2.917,-1.118 1.128,-0.338 2.391,-0.506 3.79,-0.506 1.047,0 2.013,0.127 2.899,0.383 0.885,0.255 1.647,0.623 2.287,1.101 0.641,0.477 1.142,1.059 1.502,1.746 0.361,0.687 0.543,1.473 0.543,2.358 0,0.955 -0.251,1.787 -0.752,2.498 -0.501,0.71 -1.158,1.339 -1.974,1.886 -0.815,0.547 -1.746,1.036 -2.794,1.467 -1.048,0.432 -2.113,0.891 -3.197,1.38 -1.083,0.489 -2.148,1.031 -3.195,1.624 -1.049,0.595 -1.986,1.293 -2.813,2.096 -0.827,0.804 -1.484,1.77 -1.973,2.9 -0.489,1.129 -0.734,2.45 -0.734,3.965 0,1.489 0.302,2.847 0.908,4.069 0.606,1.222 1.444,2.265 2.515,3.126 1.072,0.862 2.328,1.532 3.774,2.01 1.442,0.477 3.004,0.716 4.68,0.716 0.978,0 1.944,-0.094 2.899,-0.28 0.955,-0.186 1.822,-0.408 2.602,-0.664 0.782,-0.256 1.455,-0.53 2.027,-0.821 0.57,-0.291 0.937,-0.523 1.1,-0.698 0.163,-0.175 0.285,-0.327 0.366,-0.454 0.083,-0.129 0.153,-0.292 0.21,-0.489 0.058,-0.199 0.099,-0.437 0.123,-0.716 0.023,-0.28 0.035,-0.607 0.035,-0.979 0,-0.443 -0.012,-0.815 -0.035,-1.117 -0.024,-0.303 -0.076,-0.554 -0.157,-0.752 -0.082,-0.198 -0.176,-0.344 -0.279,-0.436 -0.106,-0.093 -0.24,-0.14 -0.402,-0.14 -0.209,0 -0.554,0.15 -1.031,0.454 -0.477,0.302 -1.072,0.623 -1.781,0.962 -0.712,0.336 -1.543,0.656 -2.498,0.96 -0.955,0.302 -2.026,0.454 -3.214,0.454 -0.908,0 -1.735,-0.123 -2.48,-0.367 -0.745,-0.244 -1.375,-0.583 -1.887,-1.014 -0.512,-0.431 -0.908,-0.942 -1.188,-1.537 -0.279,-0.593 -0.418,-1.228 -0.418,-1.903 0,-1.001 0.255,-1.863 0.768,-2.585 0.513,-0.723 1.182,-1.352 2.008,-1.887 0.827,-0.536 1.765,-1.03 2.812,-1.484 1.048,-0.453 2.121,-0.914 3.214,-1.38 C -7.709,9.874 -6.638,9.351 -5.59,8.769 -4.541,8.187 -3.604,7.499 -2.777,6.707 -1.951,5.915 -1.282,4.973 -0.769,3.878 -0.258,2.782 0,1.49 0,0" />
      </g>
      <g
         style="fill:#f8f8f8;fill-opacity:1"
         transform="matrix(1.25,0,0,-1.25,409.86094,529.55233)"
         id="g3366">
        <path
           inkscape:connector-curvature="0"
           id="path3368"
           style="fill:#f8f8f8;fill-opacity:1;fill-rule:nonzero;stroke:none"
           d="m 0,0 c 0,-0.676 -0.047,-1.205 -0.141,-1.588 -0.092,-0.385 -0.291,-0.728 -0.592,-1.03 -0.303,-0.304 -0.757,-0.646 -1.362,-1.03 -0.606,-0.385 -1.334,-0.741 -2.182,-1.066 -0.851,-0.327 -1.816,-0.599 -2.899,-0.82 -1.083,-0.222 -2.264,-0.331 -3.545,-0.331 -2.512,0 -4.806,0.412 -6.878,1.238 -2.072,0.826 -3.846,2.037 -5.325,3.633 -1.478,1.594 -2.625,3.56 -3.438,5.9 -0.816,2.339 -1.223,5.022 -1.223,8.049 0,3.002 0.412,5.691 1.239,8.066 0.826,2.374 1.985,4.387 3.474,6.041 1.49,1.653 3.277,2.909 5.361,3.771 2.083,0.86 4.382,1.292 6.896,1.292 1.094,0 2.147,-0.094 3.16,-0.279 1.013,-0.187 1.949,-0.444 2.811,-0.769 0.861,-0.326 1.634,-0.681 2.322,-1.065 0.686,-0.383 1.181,-0.728 1.484,-1.03 0.302,-0.302 0.506,-0.658 0.61,-1.066 0.106,-0.406 0.158,-0.994 0.158,-1.763 0,-0.419 -0.024,-0.78 -0.071,-1.083 -0.046,-0.301 -0.109,-0.551 -0.191,-0.75 -0.081,-0.198 -0.18,-0.343 -0.297,-0.436 -0.116,-0.093 -0.257,-0.14 -0.418,-0.14 -0.258,0 -0.606,0.18 -1.048,0.541 -0.443,0.361 -1.031,0.751 -1.763,1.17 -0.734,0.42 -1.641,0.803 -2.724,1.152 -1.083,0.35 -2.369,0.524 -3.859,0.524 -1.629,0 -3.114,-0.32 -4.452,-0.96 -1.338,-0.641 -2.479,-1.56 -3.421,-2.758 -0.943,-1.2 -1.671,-2.671 -2.183,-4.417 -0.513,-1.746 -0.768,-3.701 -0.768,-5.867 0,-2.281 0.273,-4.295 0.82,-6.041 0.547,-1.744 1.321,-3.201 2.323,-4.363 1,-1.165 2.193,-2.043 3.579,-2.637 1.384,-0.594 2.927,-0.891 4.626,-0.891 1.42,0 2.637,0.175 3.649,0.524 1.013,0.349 1.873,0.727 2.584,1.134 0.709,0.409 1.297,0.787 1.763,1.135 0.466,0.35 0.85,0.525 1.153,0.525 0.139,0 0.256,-0.035 0.349,-0.105 C -0.326,2.34 -0.25,2.205 -0.192,2.008 -0.134,1.811 -0.087,1.547 -0.053,1.223 -0.019,0.896 0,0.489 0,0" />
      </g>
      <g
         style="fill:#f8f8f8;fill-opacity:1"
         transform="matrix(1.25,0,0,-1.25,484.77432,512.55883)"
         id="g3370">
        <path
           inkscape:connector-curvature="0"
           id="path3372"
           style="fill:#f8f8f8;fill-opacity:1;fill-rule:nonzero;stroke:none"
           d="m 0,0 c 0,1.914 -0.233,3.69 -0.7,5.324 -0.468,1.635 -1.197,3.054 -2.19,4.257 -0.994,1.203 -2.277,2.143 -3.853,2.82 -1.577,0.678 -3.626,1.016 -6.15,1.016 l -5.08,0 0,-28.059 5.046,0 c 2.382,0 4.356,0.286 5.92,0.859 1.565,0.571 2.866,1.448 3.906,2.628 1.039,1.178 1.817,2.65 2.33,4.414 C -0.257,-4.979 0,-2.383 0,0 m 6.167,0.175 c 0,-3.271 -0.427,-6.618 -1.279,-9.001 -0.854,-2.382 -2.085,-4.357 -3.697,-5.921 -1.612,-1.565 -3.586,-2.727 -5.921,-3.485 -2.336,-0.759 -5.149,-1.138 -8.442,-1.138 l -8.794,0 c -0.467,0 -0.911,0.168 -1.331,0.508 -0.421,0.338 -0.631,0.892 -0.631,1.664 l 0,33.173 c 0,0.77 0.21,1.325 0.631,1.664 0.42,0.339 0.864,0.508 1.331,0.508 l 9.566,0 c 3.221,0 5.96,-0.404 8.213,-1.209 2.253,-0.806 4.146,-1.98 5.676,-3.521 C 3.019,11.876 4.18,9.989 4.975,7.759 5.769,5.528 6.167,3 6.167,0.175" />
      </g>
      <g
         style="fill:#f8f8f8;fill-opacity:1"
         transform="matrix(1.25,0,0,-1.25,522.91644,530.98046)"
         id="g3374">
        <path
           inkscape:connector-curvature="0"
           id="path3376"
           style="fill:#f8f8f8;fill-opacity:1;fill-rule:nonzero;stroke:none"
           d="m 0,0 -0.047,12.308 c 0,0 -4.015,0.141 -5.364,0.141 -1.735,0 -3.275,-0.056 -4.551,-0.349 -1.276,-0.294 -1.943,-0.631 -2.559,-1.012 -0.867,-0.535 -1.476,-1.227 -1.873,-2.03 -0.395,-0.804 -0.592,-1.73 -0.592,-2.775 0,-1.787 0.414,-3.342 1.703,-4.537 C -11.78,0.354 -9.799,0.105 -9.799,0.105 -7.561,-0.453 0,0 0,0 m 3.354,-4.633 c -6.511,0 -12.586,-0.523 -17.007,0.783 -3.197,0.946 -5.419,2.934 -6.488,4.907 -0.938,1.735 -1.212,3.256 -1.212,4.915 0,1.938 0.427,3.71 1.188,5.051 2.156,3.811 5.828,4.569 8.18,5.085 1.219,0.268 4.511,0.656 7.191,0.656 l 4.747,0 0,3.01 c 0,1.327 -0.141,2.501 -0.422,3.522 -0.281,1.02 -0.733,1.868 -1.359,2.546 -0.625,0.676 -1.435,1.185 -2.431,1.531 -0.994,0.344 -2.22,0.517 -3.674,0.517 -1.556,0 -2.954,-0.187 -4.19,-0.556 -1.239,-0.37 -2.324,-0.779 -3.256,-1.225 -0.931,-0.447 -1.709,-0.855 -2.333,-1.225 -0.626,-0.37 -1.092,-0.554 -1.398,-0.554 -0.204,0 -0.382,0.05 -0.536,0.152 -0.153,0.102 -0.287,0.255 -0.402,0.459 -0.114,0.205 -0.197,0.465 -0.249,0.786 -0.052,0.318 -0.076,0.669 -0.076,1.052 0,0.637 0.045,1.142 0.134,1.512 0.089,0.37 0.306,0.721 0.65,1.053 0.345,0.332 0.939,0.72 1.78,1.168 0.843,0.445 1.813,0.853 2.911,1.224 1.095,0.369 2.295,0.676 3.596,0.918 1.302,0.242 2.616,0.365 3.944,0.365 2.474,0 4.579,-0.282 6.315,-0.843 1.735,-0.561 3.139,-1.384 4.211,-2.469 1.071,-1.085 1.85,-2.431 2.335,-4.037 0.484,-1.608 0.727,-3.484 0.727,-5.628 l 0,-22.712 C 6.23,-3.349 5.993,-3.77 5.67,-4.091 4.973,-4.783 3.998,-4.633 3.354,-4.633" />
      </g>
      <g
         style="fill:#393536;fill-opacity:1"
         transform="matrix(1.25,0,0,-1.25,562.26332,523.50983)"
         id="g3378">
        <path
           inkscape:connector-curvature="0"
           id="path3380"
           style="fill:#393536;fill-opacity:1;fill-rule:nonzero;stroke:none"
           d="m 0,0 c 0,1.014 -0.159,1.91 -0.478,2.688 -0.318,0.779 -0.801,1.439 -1.45,1.981 -0.648,0.542 -1.462,0.955 -2.439,1.237 -0.98,0.283 -2.271,0.425 -3.873,0.425 l -6.721,0 0,-12.308 7.852,0 c 1.25,0 2.276,0.129 3.077,0.389 0.802,0.26 1.504,0.643 2.104,1.15 0.602,0.506 1.073,1.125 1.415,1.857 C -0.171,-1.852 0,-0.99 0,0 m -2.086,16.659 c 0,0.824 -0.125,1.579 -0.372,2.263 -0.248,0.683 -0.637,1.262 -1.168,1.733 -0.529,0.472 -1.201,0.837 -2.015,1.097 -0.813,0.259 -1.928,0.389 -3.342,0.389 l -5.978,0 0,-11.354 6.402,0 c 1.344,0 2.399,0.159 3.165,0.479 0.766,0.318 1.386,0.742 1.857,1.273 0.473,0.529 0.831,1.149 1.079,1.857 0.247,0.706 0.372,1.46 0.372,2.263 M 6.366,0.248 c 0,-1.227 -0.165,-2.347 -0.495,-3.36 C 5.54,-4.127 5.076,-5.041 4.474,-5.854 3.872,-6.667 3.165,-7.368 2.352,-7.959 1.539,-8.547 0.63,-9.043 -0.372,-9.443 c -1.002,-0.401 -2.086,-0.696 -3.254,-0.885 -1.166,-0.188 -2.516,-0.281 -4.048,-0.281 l -11.213,0 c -0.471,0 -0.92,0.17 -1.343,0.511 -0.425,0.342 -0.636,0.903 -0.636,1.682 l 0,32.961 c 0,0.779 0.211,1.339 0.636,1.682 0.423,0.339 0.872,0.511 1.343,0.511 l 10.47,0 c 2.239,0 4.103,-0.2 5.588,-0.601 1.485,-0.401 2.735,-1.002 3.749,-1.803 C 1.934,23.532 2.699,22.548 3.219,21.381 3.737,20.213 3.996,18.863 3.996,17.33 3.996,16.411 3.885,15.55 3.66,14.749 3.437,13.947 3.113,13.204 2.688,12.521 2.264,11.836 1.733,11.229 1.097,10.699 0.46,10.168 -0.271,9.738 -1.097,9.408 -0.083,9.219 0.878,8.878 1.787,8.383 2.693,7.889 3.484,7.244 4.155,6.455 4.828,5.665 5.364,4.751 5.765,3.713 6.166,2.676 6.366,1.521 6.366,0.248" />
      </g>
      <g
         style="fill:#393536;fill-opacity:1"
         transform="matrix(1.25,0,0,-1.25,599.29332,502.71758)"
         id="g3382">
        <path
           inkscape:connector-curvature="0"
           id="path3384"
           style="fill:#393536;fill-opacity:1;fill-rule:nonzero;stroke:none"
           d="m 0,0 c 0,0.836 -0.126,1.612 -0.376,2.328 -0.252,0.717 -0.663,1.331 -1.236,1.845 -0.574,0.514 -1.308,0.92 -2.202,1.218 -0.897,0.298 -2.11,0.448 -3.637,0.448 l -5.337,0 0,-12.108 5.086,0 c 1.48,0 2.687,0.15 3.619,0.449 0.931,0.298 1.694,0.727 2.292,1.29 0.597,0.56 1.044,1.222 1.343,1.987 C -0.15,-1.778 0,-0.931 0,0 m 8.453,-26.577 c 0,-0.168 -0.048,-0.305 -0.143,-0.413 -0.095,-0.107 -0.269,-0.197 -0.519,-0.269 -0.252,-0.071 -0.598,-0.125 -1.04,-0.161 -0.441,-0.036 -0.997,-0.054 -1.665,-0.054 -0.645,0 -1.164,0.018 -1.559,0.054 -0.392,0.036 -0.715,0.107 -0.965,0.216 -0.252,0.107 -0.437,0.244 -0.556,0.412 -0.12,0.166 -0.216,0.381 -0.287,0.645 l -3.08,8.668 c -0.359,1.001 -0.753,1.91 -1.183,2.721 -0.43,0.812 -0.931,1.499 -1.505,2.06 -0.572,0.56 -1.247,0.996 -2.023,1.308 -0.777,0.31 -1.785,0.465 -3.027,0.465 l -3.689,0 0,-15.51 c 0,-0.168 -0.048,-0.316 -0.143,-0.447 -0.095,-0.133 -0.263,-0.239 -0.502,-0.322 -0.239,-0.084 -0.549,-0.15 -0.931,-0.197 -0.382,-0.048 -0.872,-0.073 -1.469,-0.073 -0.573,0 -1.057,0.025 -1.449,0.073 -0.396,0.047 -0.712,0.113 -0.95,0.197 -0.24,0.083 -0.408,0.189 -0.502,0.322 -0.095,0.131 -0.144,0.279 -0.144,0.447 l 0,34.258 c 0,0.788 0.216,1.356 0.646,1.702 0.429,0.345 0.882,0.519 1.361,0.519 l 10.1,0 c 2.293,0 4.234,-0.227 5.822,-0.68 C 0.639,8.91 1.975,8.265 3.063,7.43 4.148,6.594 4.979,6.083 5.552,4.854 6.124,3.624 6.411,2.233 6.411,0.681 6.411,-0.562 6.238,-1.696 5.892,-2.722 5.545,-3.749 5.056,-4.674 4.424,-5.498 3.79,-6.321 3.021,-7.032 2.113,-7.63 1.205,-8.227 0.166,-8.716 -1.004,-9.099 c 0.646,-0.261 1.236,-0.589 1.775,-0.984 0.536,-0.394 1.026,-0.877 1.467,-1.451 0.443,-0.572 0.86,-1.242 1.254,-2.006 0.394,-0.765 0.781,-1.624 1.164,-2.578 l 3.188,-8.348 c 0.239,-0.621 0.4,-1.08 0.484,-1.378 0.083,-0.3 0.125,-0.544 0.125,-0.733" />
      </g>
      <text
         sodipodi:linespacing="125%"
         id="text3448"
         y="552.36218"
         x="557.14282"
         style="font-style:normal;font-weight:normal;font-size:40px;line-height:125%;font-family:sans-serif;letter-spacing:0px;word-spacing:0px;fill:#ffffff;fill-opacity:1;stroke:none;stroke-width:1px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1;"
         xml:space="preserve"><tspan
           style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:16.25px;font-family:Verdana;-inkscape-font-specification:Verdana;fill:#ffffff;"
           y="552.36218"
           x="557.14282"
           id="tspan3450"
           sodipodi:role="line">1.1 CE</tspan></text>
    </g>
  </g>
</svg>
