/*
    Mango - Open Source M2M - http://mango.serotoninsoftware.com
    Copyright (C) 2006-2011 Serotonin Software Technologies Inc.
    <AUTHOR>
    
    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */
package com.serotonin.mango.db.dao;

import java.sql.ResultSet;
import java.sql.SQLException;

import com.serotonin.db.spring.GenericRowMapper;
import com.serotonin.mango.vo.UserComment;

public class UserCommentRowMapper implements GenericRowMapper<UserComment> {
    public static final String USER_COMMENT_SELECT = "select uc.userId, u.username, uc.ts, uc.commentText "
            + "from userComments uc left join users u on uc.userId = u.id ";

    public UserComment mapRow(ResultSet rs, int rowNum) throws SQLException {
        UserComment c = new UserComment();
        c.setUserId(rs.getInt(1));
        c.setUsername(rs.getString(2));
        c.setTs(rs.getLong(3));
        c.setComment(rs.getString(4));
        return c;
    }
}
