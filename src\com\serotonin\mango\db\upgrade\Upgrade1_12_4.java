/*
    Mango - Open Source M2M - http://mango.serotoninsoftware.com
    Copyright (C) 2006-2011 Serotonin Software Technologies Inc.
    <AUTHOR>
    
    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */
package com.serotonin.mango.db.upgrade;

import java.io.OutputStream;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.serotonin.mango.Common;
import com.serotonin.mango.db.DatabaseAccess.DatabaseType;

/**
 * <AUTHOR>
 */
public class Upgrade1_12_4 extends DBUpgrade {
	private final Log log = LogFactory.getLog(getClass());

	@Override
	public void upgrade() throws Exception {
		OutputStream out = createUpdateLogOutputStream("1_13_0");

		String type = Common.getEnvironmentProfile().getString("db.type",
				"derby");
		DatabaseType dt = DatabaseType.valueOf(type.toUpperCase());

		// Run the script.
		log.info("Database: " + type.toUpperCase());
		log.info("Running script");
		switch (dt) {
		case MYSQL:
			runScript(MySQLScript, out);
			break;
		case MSSQL:
			runScript(MSSQLScript, out);
			break;
		case POSTGRES:
			runScript(PostgresScript, out);
			break;
		case ORACLE11G:
			runScript(OracleScript, out);
			break;
		default:
			runScript(DerbyScript, out);
		}

		out.flush();
		out.close();
	}

	@Override
	protected String getNewSchemaVersion() {
		return "1.13.0";
	}

	private static String[] DerbyScript = {
			"CREATE TABLE eventDetectorTemplates (",
			"id int not null generated by default as identity (start with 1, increment by 1),",
			"name varchar(255) NOT NULL,",
			"primary key (id)",
			") ;",

			"CREATE TABLE templatesDetectors (",
			"id int not null generated by default as identity (start with 1, increment by 1),",
			"xid varchar(50) NOT NULL,",
			"alias varchar(255),",
			"detectorType int NOT NULL,",
			"alarmLevel int NOT NULL,",
			"stateLimit FLOAT,",
			"duration int,",
			"durationType int,",
			"binaryState char(1),",
			"multistateState int,",
			"changeCount int,",
			"alphanumericState varchar(128),",
			"weight float,",
			"threshold double,",
			"eventDetectorTemplateId int NOT NULL,",
			"primary key (id)",
			") ;",
			"ALTER TABLE templatesDetectors ADD CONSTRAINT templatesDetectorsFk1 FOREIGN KEY (eventDetectorTemplateId) REFERENCES eventDetectorTemplates (id);",

			"CREATE TABLE usersProfiles (",
			"id int not null generated by default as identity (start with 1, increment by 1),",
			"name varchar(255) NOT NULL,",
			"xid varchar(50) not null,",
			"primary key (id)",
			") ;",

			"create table dataSourceUsersProfiles (",
			"dataSourceId int not null,",
			"userProfileId int not null",
			") ;",
			"alter table dataSourceUsersProfiles add constraint dataSourceUsersProfilesFk1 foreign key (dataSourceId) references dataSources(id) on delete cascade;",
			"alter table dataSourceUsersProfiles add constraint dataSourceUsersProfilesFk2 foreign key (userProfileId) references usersProfiles(id) on delete cascade;",

			"create table dataPointUsersProfiles (",
			"dataPointId int not null,",
			"userProfileId int not null,",
			"permission int not null",
			") ;",
			"alter table dataPointUsersProfiles add constraint dataPointUsersProfilesFk1 foreign key (dataPointId) references dataPoints(id) on delete cascade;",
			"alter table dataPointUsersProfiles add constraint dataPointUsersProfilesFk2 foreign key (userProfileId) references usersProfiles(id) on delete cascade;",

			"create table usersUsersProfiles (",
			"userProfileId int not null,",
			"userId int not null",
			") ;",
			"alter table usersUsersProfiles add constraint usersUsersProfilesFk1 foreign key (userProfileId) references usersProfiles(id);",
			"alter table usersUsersProfiles add constraint usersUsersProfilesFk2 foreign key (userId) references users(id) on delete cascade;",

			"create table watchListUsersProfiles (",
			"watchlistId int not null,",
			"userProfileId int not null,",
			"permission int not null",
			") ;",
			"alter table watchListUsersProfiles add constraint watchListUsersProfilesFk1 foreign key (watchlistId) references watchLists(id) on delete cascade;",
			"alter table watchListUsersProfiles add constraint watchListUsersProfilesFk2 foreign key (userProfileId) references usersProfiles(id) on delete cascade;",

			"create table viewUsersProfiles (",
			"viewId int not null,",
			"userProfileId int not null,",
			"permission int not null",
			") ;",
			"alter table viewUsersProfiles add constraint viewUsersProfilesFk1 foreign key (viewId) references mangoViews(id) on delete cascade;",
			"alter table viewUsersProfiles add constraint viewUsersProfilesFk2 foreign key (userProfileId) references usersProfiles(id) on delete cascade;" };

	private static String[] MSSQLScript = {
			"CREATE TABLE eventDetectorTemplates (",
			"id int NOT NULL auto_increment,",
			"name varchar(255) NOT NULL,",
			"primary key (id)",
			") ;",

			"CREATE TABLE templatesDetectors (",
			" id int NOT NULL auto_increment,",
			"xid varchar(50) NOT NULL,",
			"alias varchar(255),",
			"detectorType int NOT NULL,",
			"alarmLevel int NOT NULL,",
			"stateLimit FLOAT,",
			"duration int,",
			"durationType int,",
			"binaryState char(1),",
			"multistateState int,",
			"changeCount int,",
			"alphanumericState varchar(128),",
			"weight float,",
			"threshold double,",
			"eventDetectorTemplateId int NOT NULL,",
			"primary key (id)",
			") ;",
			"ALTER TABLE templatesDetectors ADD CONSTRAINT templatesDetectorsFk1 FOREIGN KEY (eventDetectorTemplateId) REFERENCES eventDetectorTemplates (id);",

			"CREATE TABLE usersProfiles (",
			"id int NOT NULL auto_increment,",
			"name varchar(255) NOT NULL,",
			"xid varchar(50) not null,",
			"primary key (id)",
			") ;",

			"create table dataSourceUsersProfiles (",
			"dataSourceId int not null,",
			"userProfileId int not null",
			") ;",
			"alter table dataSourceUsersProfiles add constraint dataSourceUsersProfilesFk1 foreign key (dataSourceId) references dataSources(id) on delete cascade;",
			"alter table dataSourceUsersProfiles add constraint dataSourceUsersProfilesFk2 foreign key (userProfileId) references usersProfiles(id) on delete cascade;",

			"create table dataPointUsersProfiles (",
			"dataPointId int not null,",
			"userProfileId int not null,",
			"permission int not null",
			") ;",
			"alter table dataPointUsersProfiles add constraint dataPointUsersProfilesFk1 foreign key (dataPointId) references dataPoints(id) on delete cascade;",
			"alter table dataPointUsersProfiles add constraint dataPointUsersProfilesFk2 foreign key (userProfileId) references usersProfiles(id) on delete cascade;",

			"create table usersUsersProfiles (",
			"userProfileId int not null,",
			"userId int not null",
			") ;",
			"alter table usersUsersProfiles add constraint usersUsersProfilesFk1 foreign key (userProfileId) references usersProfiles(id);",
			"alter table usersUsersProfiles add constraint usersUsersProfilesFk2 foreign key (userId) references users(id) on delete cascade;",

			"create table watchListUsersProfiles (",
			"watchlistId int not null,",
			"userProfileId int not null,",
			"permission int not null",
			") ;",
			"alter table watchListUsersProfiles add constraint watchListUsersProfilesFk1 foreign key (watchlistId) references watchLists(id) on delete cascade;",
			"alter table watchListUsersProfiles add constraint watchListUsersProfilesFk2 foreign key (userProfileId) references usersProfiles(id) on delete cascade;",

			"create table viewUsersProfiles (",
			"viewId int not null,",
			"userProfileId int not null,",
			"permission int not null",
			") ;",
			"alter table viewUsersProfiles add constraint viewUsersProfilesFk1 foreign key (viewId) references mangoViews(id) on delete cascade;",
			"alter table viewUsersProfiles add constraint viewUsersProfilesFk2 foreign key (userProfileId) references usersProfiles(id) on delete cascade;" };

	private static String[] OracleScript = {
	// No OracleDA before, so nothing here!
	};

	private static String[] PostgresScript = {
			"CREATE TABLE eventDetectorTemplates (",
			"id serial NOT NULL,",
			"name varchar(255) NOT NULL,",
			"primary key (id)",
			");",

			"CREATE TABLE templatesDetectors (",
			"id serial NOT NULL,",
			"xid varchar(50) NOT NULL,",
			"alias varchar(255),",
			"detectorType int NOT NULL,",
			"alarmLevel int NOT NULL,",
			"stateLimit FLOAT,",
			"duration int,",
			"durationType int,",
			"binaryState char(1),",
			"multistateState int,",
			"changeCount int,",
			"alphanumericState varchar(128),",
			"weight float,",
			"threshold double,",
			"eventDetectorTemplateId int NOT NULL,",
			"primary key (id)",
			");",
			"ALTER TABLE templatesDetectors ADD CONSTRAINT templatesDetectorsFk1 FOREIGN KEY (eventDetectorTemplateId) REFERENCES eventDetectorTemplates (id);",

			"CREATE TABLE usersProfiles (",
			"id serial NOT NULL,",
			"name varchar(255) NOT NULL,",
			"xid varchar(50) not null,",
			"primary key (id)",
			");",

			"create table dataSourceUsersProfiles (",
			"dataSourceId int not null,",
			"userProfileId int not null",
			");",
			"alter table dataSourceUsersProfiles add constraint dataSourceUsersProfilesFk1 foreign key (dataSourceId) references dataSources(id) on delete cascade;",
			"alter table dataSourceUsersProfiles add constraint dataSourceUsersProfilesFk2 foreign key (userProfileId) references usersProfiles(id) on delete cascade;",

			"create table dataPointUsersProfiles (",
			"dataPointId int not null,",
			"userProfileId int not null,",
			"permission int not null",
			");",
			"alter table dataPointUsersProfiles add constraint dataPointUsersProfilesFk1 foreign key (dataPointId) references dataPoints(id) on delete cascade;",
			"alter table dataPointUsersProfiles add constraint dataPointUsersProfilesFk2 foreign key (userProfileId) references usersProfiles(id) on delete cascade;",

			"create table usersUsersProfiles (",
			"userProfileId int not null,",
			"userId int not null",
			");",
			"alter table usersUsersProfiles add constraint usersUsersProfilesFk1 foreign key (userProfileId) references usersProfiles(id);",
			"alter table usersUsersProfiles add constraint usersUsersProfilesFk2 foreign key (userId) references users(id) on delete cascade;",

			"create table watchListUsersProfiles (",
			"watchlistId int not null,",
			"userProfileId int not null,",
			"permission int not null",
			");",
			"alter table watchListUsersProfiles add constraint watchListUsersProfilesFk1 foreign key (watchlistId) references watchLists(id) on delete cascade;",
			"alter table watchListUsersProfiles add constraint watchListUsersProfilesFk2 foreign key (userProfileId) references usersProfiles(id) on delete cascade;",

			"create table viewUsersProfiles (",
			"viewId int not null,",
			"userProfileId int not null,",
			"permission int not null",
			");",
			"alter table viewUsersProfiles add constraint viewUsersProfilesFk1 foreign key (viewId) references mangoViews(id) on delete cascade;",
			"alter table viewUsersProfiles add constraint viewUsersProfilesFk2 foreign key (userProfileId) references usersProfiles(id) on delete cascade;" };

	private static String[] MySQLScript = {
			"CREATE TABLE eventDetectorTemplates (",
			"id int NOT NULL auto_increment,",
			"name varchar(255) NOT NULL,",
			"primary key (id)",
			") ENGINE=InnoDB;",

			"CREATE TABLE templatesDetectors (",
			" id int NOT NULL auto_increment,",
			"xid varchar(50) NOT NULL,",
			"alias varchar(255),",
			"detectorType int NOT NULL,",
			"alarmLevel int NOT NULL,",
			"stateLimit FLOAT,",
			"duration int,",
			"durationType int,",
			"binaryState char(1),",
			"multistateState int,",
			"changeCount int,",
			"alphanumericState varchar(128),",
			"weight float,",
			"threshold double,",
			"eventDetectorTemplateId int NOT NULL,",
			"primary key (id)",
			") ENGINE=InnoDB;",
			"ALTER TABLE templatesDetectors ADD CONSTRAINT templatesDetectorsFk1 FOREIGN KEY (eventDetectorTemplateId) REFERENCES eventDetectorTemplates (id);",

			"CREATE TABLE usersProfiles (",
			"id int NOT NULL auto_increment,",
			"name varchar(255) NOT NULL,",
			"xid varchar(50) not null,",
			"primary key (id)",
			") ENGINE=InnoDB;",

			"create table dataSourceUsersProfiles (",
			"dataSourceId int not null,",
			"userProfileId int not null",
			") ENGINE=InnoDB;",
			"alter table dataSourceUsersProfiles add constraint dataSourceUsersProfilesFk1 foreign key (dataSourceId) references dataSources(id) on delete cascade;",
			"alter table dataSourceUsersProfiles add constraint dataSourceUsersProfilesFk2 foreign key (userProfileId) references usersProfiles(id) on delete cascade;",

			"create table dataPointUsersProfiles (",
			"dataPointId int not null,",
			"userProfileId int not null,",
			"permission int not null",
			") ENGINE=InnoDB;",
			"alter table dataPointUsersProfiles add constraint dataPointUsersProfilesFk1 foreign key (dataPointId) references dataPoints(id) on delete cascade;",
			"alter table dataPointUsersProfiles add constraint dataPointUsersProfilesFk2 foreign key (userProfileId) references usersProfiles(id) on delete cascade;",

			"create table usersUsersProfiles (",
			"userProfileId int not null,",
			"userId int not null",
			") ENGINE=InnoDB;",
			"alter table usersUsersProfiles add constraint usersUsersProfilesFk1 foreign key (userProfileId) references usersProfiles(id);",
			"alter table usersUsersProfiles add constraint usersUsersProfilesFk2 foreign key (userId) references users(id) on delete cascade;",

			"create table watchListUsersProfiles (",
			"watchlistId int not null,",
			"userProfileId int not null,",
			"permission int not null",
			") ENGINE=InnoDB;",
			"alter table watchListUsersProfiles add constraint watchListUsersProfilesFk1 foreign key (watchlistId) references watchLists(id) on delete cascade;",
			"alter table watchListUsersProfiles add constraint watchListUsersProfilesFk2 foreign key (userProfileId) references usersProfiles(id) on delete cascade;",

			"create table viewUsersProfiles (",
			"viewId int not null,",
			"userProfileId int not null,",
			"permission int not null",
			") ENGINE=InnoDB;",
			"alter table viewUsersProfiles add constraint viewUsersProfilesFk1 foreign key (viewId) references mangoViews(id) on delete cascade;",
			"alter table viewUsersProfiles add constraint viewUsersProfilesFk2 foreign key (userProfileId) references usersProfiles(id) on delete cascade;" };
}
