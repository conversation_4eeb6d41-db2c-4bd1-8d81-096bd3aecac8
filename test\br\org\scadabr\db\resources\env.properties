#
#    Mango - Open Source M2M - http://mango.serotoninsoftware.com
#    Copyright (C) 2006-2009 Serotonin Software Technologies Inc.
#    <AUTHOR>
#    
#    This program is free software: you can redistribute it and/or modify
#    it under the terms of the GNU General Public License as published by
#    the Free Software Foundation, either version 3 of the License, or
#    (at your option) any later version.
#
#    This program is distributed in the hope that it will be useful,
#    but WITHOUT ANY WARRANTY; without even the implied warranty of
#    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#    GNU General Public License for more details.
#
#    You should have received a copy of the GNU General Public License
#    along with this program.  If not, see <http://www.gnu.org/licenses/>.
#
#

##
# ORACLE11G
# db.url=jdbc:oracle:thin:@<SERVER NAME>:<PORT NUMBER>:<DATABASE SID>;
#
#db.type=oracle11g
#db.url=jdbc:oracle:thin:@**************:1521:xe
#db.username=system
#db.password=manager
#db.pool.maxActive=10
#db.pool.maxIdle=10

# PARA INTEGRAR O ScadaBR AO POSTGRESQL v9.2 INSTALE O POSTGRESQL v9.2 NA M\u00c1QUINA SERVIDORA
# ALTERE A LINHA db.password=admin (COLOQUE A SENHA DEFINIDA POR VOC\u00ca)
# N\u00c3O REMOVA A LINHA db.url.public=*****************************************

#db.type=postgres
#db.url=****************************************
#db.url.public=*****************************************
#db.username=postgres
#db.password=admin
#db.pool.maxActive=10
#db.pool.maxIdle=10

db.type=mysql
db.url=******************************
db.username=root
db.password=12345
db.pool.maxActive=10
db.pool.maxIdle=10

#db.type=derby
#db.url=~../../bin/scadabrDB
#db.username=
#db.password=
#
#convert.db.type=
#convert.db.url=
#convert.db.username=
#convert.db.password=

api.authentication=disabled
api.username=admin
api.password=admin

#security.hashAlgorithm=NONE
grove.url=http://mango.serotoninsoftware.com/servlet
