<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN"
	"http://www.w3.org/TR/html4/strict.dtd">
<html>
	<head>
		<title>Dojo Demos</title>
		<script type="text/javascript">
			var djConfig = { isDebug: true };
		</script>
		<link rel=stylesheet href="demoEngine.css" type="text/css">

		<script type="text/javascript" src="../dojo.js"></script>

		<script type="text/javascript">
			dojo.require("dojo.debug.Firebug");
			dojo.require("dojo.event.*");
			dojo.require("dojo.widget.*");
			dojo.require("dojo.widget.demoEngine.*");
		</script>
	</head>
	<body>
		<div dojoType="DemoNavigator"
				demoRegistryUrl="demoRegistry.json" 
				returnImage="dojoDemos.gif"
				viewDemoImage="viewDemo.png"></div>
	</body>
</html>
