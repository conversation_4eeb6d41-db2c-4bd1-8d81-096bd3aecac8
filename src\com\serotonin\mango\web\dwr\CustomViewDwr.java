/*
    Mango - Open Source M2M - http://mango.serotoninsoftware.com
    Copyright (C) 2006-2011 Serotonin Software Technologies Inc.
    <AUTHOR>
    
    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */
package com.serotonin.mango.web.dwr;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.directwebremoting.WebContextFactory;

import com.serotonin.mango.Common;
import com.serotonin.mango.rt.RuntimeManager;
import com.serotonin.mango.view.custom.CustomView;
import com.serotonin.mango.view.custom.CustomViewComponent;
import com.serotonin.mango.web.dwr.beans.CustomComponentState;

/**
 * <AUTHOR> Lohbihler
 */
public class CustomViewDwr extends BaseDwr {
    public List<CustomComponentState> getViewPointData() {
        CustomView view = Common.getCustomView();
        if (view == null)
            return Collections.emptyList();

        HttpServletRequest request = WebContextFactory.get().getHttpServletRequest();
        List<CustomComponentState> states = new ArrayList<CustomComponentState>();
        RuntimeManager rtm = Common.ctx.getRuntimeManager();

        for (CustomViewComponent comp : view.getComponents())
            states.add(comp.createState(rtm, request));

        return states;
    }

    public void setCustomPoint(String xid, String valueStr) {
        CustomView view = Common.getCustomView();
        setPointImpl(view.getPoint(xid), valueStr, view.getAuthorityUser());
    }
}
