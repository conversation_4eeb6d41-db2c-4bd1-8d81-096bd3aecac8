/*
    Mango - Open Source M2M - http://mango.serotoninsoftware.com
    Copyright (C) 2006-2011 Serotonin Software Technologies Inc.
    <AUTHOR>
    
    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */
package com.serotonin.mango.rt.dataSource.galil;

import com.serotonin.mango.rt.dataImage.types.AlphanumericValue;
import com.serotonin.mango.rt.dataImage.types.MangoValue;
import com.serotonin.mango.vo.dataSource.galil.CommandPointTypeVO;

/**
 * <AUTHOR>
 */
public class CommandPointTypeRT extends PointTypeRT {
    public CommandPointTypeRT(CommandPointTypeVO vo) {
        super(vo);
    }

    @Override
    public String getPollRequestImpl() {
        return null;
    }

    @Override
    public MangoValue parsePollResponse(String data, String pointName) {
        return null;
    }

    @Override
    protected String getSetRequestImpl(MangoValue value) {
        return value.getStringValue();
    }

    @Override
    public MangoValue parseSetResponse(String data) {
        return new AlphanumericValue(data);
    }
}
