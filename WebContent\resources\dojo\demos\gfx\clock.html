﻿<html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<head>
<title>dojo.gfx: interactive analog clock</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<script type="text/javascript" src="../../dojo.js"></script>
<script type="text/javascript">

dojo.require("dojo.gfx.*");
dojo.require("dojo.event.*");
dojo.require("dojo.html.layout");
dojo.require("dojo.html.style");
dojo.require("dojo.date.format");

var current_time = new Date();

var hour_hand   = null;
var minute_hand = null;
var second_hand = null;

var hour_shadow   = null;
var minute_shadow = null;
var second_shadow = null;

var center = {x: 385 / 2, y: 385 / 2};

var hour_shadow_shift   = {dx: 2, dy: 2};
var minute_shadow_shift = {dx: 3, dy: 3};
var second_shadow_shift = {dx: 4, dy: 4};

var selected_hand = null;
var container = null;
var container_position = null;
var text_time = null;
var diff_time = new Date();

placeHand = function(shape, angle, shift) {
	var move = {dx: center.x + (shift ? shift.dx : 0), dy: center.y + (shift ? shift.dy : 0)};
	return shape.setTransform([move, dojo.gfx.matrix.rotateg(-angle)]);
};

placeHourHand = function(h, m, s) {
	var angle = 30 * (h % 12 + m / 60 + s / 3600);
	placeHand(hour_hand, angle);
	placeHand(hour_shadow, angle, hour_shadow_shift);
};

placeMinuteHand = function(m, s) {
	var angle = 6 * (m + s / 60);
	placeHand(minute_hand, angle);
	placeHand(minute_shadow, angle, minute_shadow_shift);
};

placeSecondHand = function(s) {
	var angle = 6 * s;
	placeHand(second_hand, angle);
	placeHand(second_shadow, angle, second_shadow_shift);
};

reflectTime = function(time, hold_second_hand, hold_minute_hand, hold_hour_hand) {
	if(!time) time = current_time;
	var h = time.getHours();
	var m = time.getMinutes();
	var s = time.getSeconds();
	if(!hold_hour_hand) placeHourHand(h, m, s);
	if(!hold_minute_hand) placeMinuteHand(m, s);
	if(!hold_second_hand) placeSecondHand(s);
	text_time.innerHTML = dojo.date.format(
		time, {selector: "timeOnly", timePattern: "h:mm:ss a"});
};

resetTime = function() {
	current_time = new Date();
	reflectTime();
};

tick = function() {
	current_time.setSeconds(current_time.getSeconds() + 1);
	reflectTime();
};

advanceTime = function() {
	if(!selected_hand) {
		tick();
	}
};

normalizeAngle = function(angle) {
	if(angle > Math.PI) {
		angle -= 2 * Math.PI;
	} else if(angle < -Math.PI) {
		angle += 2 * Math.PI;
	}
	return angle;
};

calculateAngle = function(x, y, handAngle) {
	try {
		return normalizeAngle(Math.atan2(y - center.y, x - center.x) - handAngle);
	} catch(e) {
		// supress
	}
	return 0;
};

getSecondAngle = function(time) {
	if(!time) time = current_time;
	return (6 * time.getSeconds() - 90) / 180 * Math.PI;
};

getMinuteAngle = function(time) {
	if(!time) time = current_time;
	return (6 * (time.getMinutes() + time.getSeconds() / 60) - 90) / 180 * Math.PI;
};

getHourAngle = function(time) {
	if(!time) time = current_time;
	return (30 * (time.getHours() + (time.getMinutes() + time.getSeconds() / 60) / 60) - 90) / 180 * Math.PI;
};

onMouseDown = function(event) {
	selected_hand = event.target;
	diff_time.setTime(current_time.getTime());
	dojo.event.browser.stopEvent(event);
};

onMouseMove = function(event) {
	if(!selected_hand) return;
	if(event.target == second_hand.getEventSource() || 
			event.target == minute_hand.getEventSource() || 
			event.target == hour_hand.getEventSource()) {
		dojo.event.browser.stopEvent(event);
		return;
	}
	if(selected_hand == second_hand.getEventSource() ) {
		var angle = calculateAngle(
			event.clientX - container_position.x, 
			event.clientY - container_position.y, 
			normalizeAngle(getSecondAngle())
		);
		var diff = Math.round(angle / Math.PI * 180 / 6); // in whole seconds
		current_time.setSeconds(current_time.getSeconds() + Math.round(diff));
		reflectTime();
	} else if(selected_hand == minute_hand.getEventSource() ) {
		var angle = calculateAngle(
			event.clientX - container_position.x, 
			event.clientY - container_position.y, 
			normalizeAngle(getMinuteAngle(diff_time))
		);
		var diff = Math.round(angle / Math.PI * 180 / 6 * 60); // in whole seconds
		diff_time.setTime(diff_time.getTime() + 1000 * diff);
		reflectTime(diff_time, true);
		
	} else if(selected_hand == hour_hand.getEventSource() ) {
		var angle = calculateAngle(
			event.clientX - container_position.x, 
			event.clientY - container_position.y, 
			normalizeAngle(getHourAngle(diff_time))
		);
		var diff = Math.round(angle / Math.PI * 180 / 30 * 60 * 60); // in whole seconds
		diff_time.setTime(diff_time.getTime() + 1000 * diff);
		reflectTime(diff_time, true, true);
	} else {
		return;
	}
	dojo.event.browser.stopEvent(event);
};

onMouseUp = function(event) {
	if(selected_hand != second_hand.getEventSource()) {
		current_time.setTime(diff_time.getTime());
		reflectTime();
	}
	selected_hand = null;
	dojo.event.browser.stopEvent(event);
};

makeShapes = function(){
	// prerequisites
	container = dojo.byId("gfx_holder");
	container_position = dojo.html.abs(container);
	text_time = dojo.byId("time");
	var surface = dojo.gfx.createSurface(container, 385, 385);
    surface.createRect({width: 385, height: 385});
    surface.createImage({width: 385, height: 385, src: "images/clock_face.jpg"});
    
    // hand shapes
    var hour_hand_points = [{x: -7, y: 15}, {x: 7, y: 15}, {x: 0, y: -60}, {x: -7, y: 15}];
    var minute_hand_points = [{x: -5, y: 15}, {x: 5, y: 15}, {x: 0, y: -100}, {x: -5, y: 15}];
    var second_hand_points = [{x: -2, y: 15}, {x: 2, y: 15}, {x: 2, y: -105}, {x: 6, y: -105}, {x: 0, y: -116}, {x: -6, y: -105}, {x: -2, y: -105}, {x: -2, y: 15}];
    
    // create shapes
    hour_shadow = surface.createPolyline(hour_hand_points)
		.setFill([0, 0, 0, 0.1])
		;
    hour_hand = surface.createPolyline(hour_hand_points)
		.setStroke({color: "black", width: 2})
		.setFill("#889")
		;
    minute_shadow = surface.createPolyline(minute_hand_points)
		.setFill([0, 0, 0, 0.1])
		;
    minute_hand = surface.createPolyline(minute_hand_points)
		.setStroke({color: "black", width: 2})
		.setFill("#ccd")
		;
    second_shadow = surface.createPolyline(second_hand_points)
		.setFill([0, 0, 0, 0.1])
		;
    second_hand = surface.createPolyline(second_hand_points)
		.setStroke({color: "#800", width: 1})
		.setFill("#d00")
		;
	dojo.html.setClass(hour_hand  .getEventSource(), "movable");
	dojo.html.setClass(minute_hand.getEventSource(), "movable");
	dojo.html.setClass(second_hand.getEventSource(), "movable");
	surface.createCircle({r: 1}).setFill("black").setTransform({dx: 192.5, dy: 192.5});
	
	// attach events
	dojo.event.connect(hour_hand  .getEventSource(), "onmousedown", onMouseDown);
	dojo.event.connect(minute_hand.getEventSource(), "onmousedown", onMouseDown);
	dojo.event.connect(second_hand.getEventSource(), "onmousedown", onMouseDown);
	dojo.event.connect(container, "onmousemove", onMouseMove);
	dojo.event.connect(container, "onmouseup",   onMouseUp);
	dojo.event.connect(dojo.byId("reset"), "onclick", resetTime);

	// start the clock		
	resetTime();
	window.setInterval(advanceTime, 1000);
};

dojo.addOnLoad(makeShapes);

</script>
<style type="text/css">
.movable { cursor: hand; }
</style>
</head>
<body>
	<h1>dojo.gfx: interactive analog clock</h1>
	<p>Grab hands and set your own time.</p>
	<div id="gfx_holder" style="width: 385px; height: 385px;"></div>
	<p>Current time: <span id="time"></span>.</p>
	<p><button id="reset">Reset</button></p>
</body>
</html>
