<?xml version="1.0" encoding="utf-8"?>
<GraphicBorderSkin
	xmlns:mx="http://www.adobe.com/2006/mxml"
	xmlns="http://www.degrafa.com/2007"
	xmlns:flash.filters="flash.filters.*">

		<fills>
			<LinearGradientFill id="MainFill" angle="90" >
				<GradientStop ratio="0" alpha="1" color="#EE9819"/>
				<GradientStop ratio="1" alpha="1" color="#FAE38F"/>
			</LinearGradientFill>
			<LinearGradientFill id="RedFill" angle="180" >
				<GradientStop ratio="0" alpha="1" color="#FA6C6C"/>
				<GradientStop ratio="1" alpha="1" color="#FF0000"/>
			</LinearGradientFill>
			<LinearGradientFill id="BlueGreyFill" angle="180" >
				<GradientStop ratio="0" alpha="1" color="#BCD5E4"/>
				<GradientStop ratio="1" alpha="1" color="#87ADC3"/>
			</LinearGradientFill>
			<LinearGradientFill id="BlueGreyFillRev" angle="0" >
				<GradientStop ratio="0" alpha="1" color="#87ADC3"/>
				<GradientStop ratio="1" alpha="1" color="#87ADC3"/>
			</LinearGradientFill>
			<LinearGradientFill id="MainFillREV" angle="90" >
				<GradientStop ratio="0" alpha="1" color="#FAE38F"/>
				<GradientStop ratio="1" alpha="1" color="#EE9819"/>
			</LinearGradientFill>
					
			<LinearGradientFill id="indicatorFill" angle="0" >
				<GradientStop ratio="0" alpha="1" color="#00AEEF"/>
				<GradientStop ratio=".25" alpha="1" color="#12FF00"/>
				<GradientStop ratio=".5" alpha="1" color="#FFFC00"/>
				<GradientStop ratio="1" alpha="1" color="#FF0000"/>
			</LinearGradientFill>
			
			<LinearGradientFill id="WashFill" angle="180" >
				<GradientStop ratio="0" alpha=".5" color="#FFFFFF"/>
				<GradientStop ratio="1" alpha="0" color="#FFFFFF"/>
			</LinearGradientFill>
			<LinearGradientFill id="WashFillLight" angle="180" >
				<GradientStop ratio="0" alpha=".25" color="#FFFFFF"/>
				<GradientStop ratio=".75" alpha="0" color="#FFFFFF"/>
				<GradientStop ratio="1" alpha=".2" color="#366079"/>
			</LinearGradientFill>
			
			<LinearGradientFill id="WashFillRev" angle="-90" >
				<GradientStop ratio="0" alpha=".60" color="#FFFFFF"/>
				<GradientStop ratio="1" alpha="0" color="#FFFFFF"/>
			</LinearGradientFill>
			<SolidFill id="Highlight" alpha=".3" color="#FFFFFF"/>
		</fills>
	
	<strokes>
		<SolidStroke color="#000000" alpha="1" id="BasicStroke" />
		
		<LinearGradientStroke weight="1" id="insetEdge" pixelHinting="true">
			<GradientStop ratio="0" alpha=".3" color="#366079"/>
			<GradientStop ratio=".5" alpha="0" color="#366079"/>
			<GradientStop ratio="1" alpha=".3" color="#366079"/>
		</LinearGradientStroke>
	</strokes>
	
	<filters>
		<flash.filters:DropShadowFilter alpha=".25"/>
	</filters>

		<geometry>

		<RoundedRectangle x="-20" y="-32" cornerRadius="14" 
			 width="250" height="65" fill="{BlueGreyFill}">
		</RoundedRectangle>
		
		<Circle centerX="200" centerY="3" radius="16" fill="{BlueGreyFill}" stroke="{insetEdge}"/>
		<RoundedRectangle x="-4" y="-4" cornerRadius="12" 
			 width="194" height="14" fill="{BlueGreyFillRev}" stroke="{insetEdge}" />
		
		<RoundedRectangle x="0" y="0" cornerRadius="6" 
			 width="190" height="6" fill="{indicatorFill}">
		</RoundedRectangle>
		
		<RoundedRectangle x="0" y="2" cornerRadius="4" 
			 width="186" height="4" fill="{Highlight}"/>
		
		<Circle centerX="200" centerY="3" radius="12" fill="{RedFill}"/>
		<Ellipse height="20" width="14" x="196" y="-7" fill="{WashFill}"/>
		
		<RoundedRectangleComplex
			 topLeftRadius="8" bottomLeftRadius="8"
			 topRightRadius="8" bottomRightRadius="8" x="-16" y="-28"
			 width="242" height="58" stroke="{insetEdge}" />
		
		<RoundedRectangleComplex
			 topLeftRadius="8" bottomLeftRadius="8"
			 topRightRadius="8" bottomRightRadius="8" x="-16" y="-28"
			 width="242" height="58" fill="{WashFillLight}"/>
			
	</geometry>
	
		
</GraphicBorderSkin>
