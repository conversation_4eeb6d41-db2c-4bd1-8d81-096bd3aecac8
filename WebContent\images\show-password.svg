<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->

<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   width="30"
   height="30"
   viewBox="0 0 7.9375 7.9375"
   version="1.1"
   id="svg8"
   inkscape:version="0.92.3 (2405546, 2018-03-11)"
   sodipodi:docname="show-password.svg"
   inkscape:export-filename="/home/<USER>/eclipse-workspace/show-password.png"
   inkscape:export-xdpi="96"
   inkscape:export-ydpi="96">
  <defs
     id="defs2" />
  <sodipodi:namedview
     id="base"
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1.0"
     inkscape:pageopacity="0.0"
     inkscape:pageshadow="2"
     inkscape:zoom="15.733333"
     inkscape:cx="15"
     inkscape:cy="15"
     inkscape:document-units="mm"
     inkscape:current-layer="layer1"
     showgrid="false"
     fit-margin-top="0"
     fit-margin-left="0"
     fit-margin-right="0"
     fit-margin-bottom="0"
     units="px"
     inkscape:window-width="1366"
     inkscape:window-height="703"
     inkscape:window-x="0"
     inkscape:window-y="0"
     inkscape:window-maximized="1"
     inkscape:snap-bbox="true"
     inkscape:bbox-nodes="true" />
  <metadata
     id="metadata5">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title />
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <g
     inkscape:label="Camada 1"
     inkscape:groupmode="layer"
     id="layer1"
     transform="translate(-16.884358,-190.96614)">
    <rect
       style="opacity:1;fill:#ffffff;fill-opacity:0;fill-rule:nonzero;stroke:none;stroke-width:0.6774193;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;paint-order:normal"
       id="rect874"
       width="7.9375"
       height="7.9375"
       x="16.884357"
       y="190.96614" />
    <g
       id="g909"
       transform="translate(-0.05045073,-1.2484113)">
      <circle
         transform="scale(1,-1)"
         r="1.3486373"
         cy="-196.18306"
         cx="20.903559"
         id="path815"
         style="opacity:1;fill:#000000;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:0.03004911;stroke-linecap:square;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;paint-order:normal" />
      <path
         transform="scale(1,-1)"
         d="m 23.359723,-196.18306 a 2.4561644,2.4561644 0 0 1 -1.228082,2.1271 2.4561644,2.4561644 0 0 1 -2.456165,0 2.4561644,2.4561644 0 0 1 -1.228082,-2.1271"
         sodipodi:open="true"
         sodipodi:end="3.1415927"
         sodipodi:start="0"
         sodipodi:ry="2.4561644"
         sodipodi:rx="2.4561644"
         sodipodi:cy="-196.18306"
         sodipodi:cx="20.903559"
         sodipodi:type="arc"
         style="opacity:1;fill:none;fill-opacity:1;fill-rule:nonzero;stroke:#000000;stroke-width:0.79375005;stroke-linecap:square;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;paint-order:normal"
         id="circle817" />
      <circle
         r="2.4561644"
         cy="196.1833"
         cx="20.903559"
         id="path904"
         style="opacity:1;fill:none;fill-opacity:1;fill-rule:nonzero;stroke:#000000;stroke-width:0.79375005;stroke-linecap:square;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:0.00760456;paint-order:normal" />
    </g>
  </g>
</svg>
