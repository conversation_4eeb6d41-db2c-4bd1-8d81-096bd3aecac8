/*
    Mango - Open Source M2M - http://mango.serotoninsoftware.com
    Copyright (C) 2006-2011 Serotonin Software Technologies Inc.
    <AUTHOR>
    
    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */
package com.serotonin.mango.rt.dataSource.http;

import java.util.concurrent.CopyOnWriteArraySet;

import com.serotonin.util.IpAddressUtils;
import com.serotonin.util.IpWhiteListException;
import com.serotonin.util.StringUtils;

/**
 * <AUTHOR>
 */
public class HttpReceiverMulticaster {
	private final CopyOnWriteArraySet<HttpMulticastListener> listeners = new CopyOnWriteArraySet<HttpMulticastListener>();

	public void addListener(HttpMulticastListener l) {
		listeners.add(l);
	}

	public void removeListener(HttpMulticastListener l) {
		listeners.remove(l);
	}

	public void multicast(HttpReceiverData data) {
		for (HttpMulticastListener l : listeners) {
			// Check if the listener cares about stuff from this ip address.
			try {
				if (!IpAddressUtils.ipWhiteListCheck(l.getIpWhiteList(),
						data.getRemoteIp()))
					continue;
			} catch (IpWhiteListException e) {
				l.ipWhiteListError(e.getMessage());
				continue;
			}

			// Check if the listener cares about stuff from this device id.
			if (!StringUtils.isEmpty(data.getDeviceId())) {
				if (!StringUtils.globWhiteListMatchIgnoreCase(
						l.getDeviceIdWhiteList(), data.getDeviceId()))
					continue;
			}

			// if deviceId was not provided, only accept data if white list
			// contains a "*"
			if (data.getDeviceId() == null) {
				boolean found = false;
				for (String s : l.getDeviceIdWhiteList()) {
					if (s.equals("*")) {
						found = true;
					}
				}
				if (!found) {
					continue;
				}
			}

			// Everything checks out, so tell the listener about this data.
			l.data(data);
		}
	}
}
