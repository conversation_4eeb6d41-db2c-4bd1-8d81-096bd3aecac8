SET FOREIGN_KEY_CHECKS = 0;
DROP TABLE IF EXISTS mailingLists;
DROP TABLE IF EXISTS maintenanceEvents;
DROP TABLE IF EXISTS mangoViews;
DROP TABLE IF EXISTS mangoViewUsers;
DROP TABLE IF EXISTS pointEventDetectors;
DROP TABLE IF EXISTS pointHierarchy;
DROP TABLE IF EXISTS pointLinks;
DROP TABLE IF EXISTS pointValueAnnotations;
DROP TABLE IF EXISTS compoundEventDetectors;
DROP TABLE IF EXISTS dataPoints;
DROP TABLE IF EXISTS dataPointUsers;
DROP TABLE IF EXISTS dataSources;
DROP TABLE IF EXISTS dataSourceUsers;
DROP TABLE IF EXISTS eventHandlers;
DROP TABLE IF EXISTS events;
DROP TABLE IF EXISTS flexProjects;
DROP TABLE IF EXISTS mailingListInactive;
DROP TABLE IF EXISTS mailingListMembers;
DROP TABLE IF EXISTS pointValues;
DROP TABLE IF EXISTS publishers;
DROP TABLE IF EXISTS reportInstanceData;
DROP TABLE IF EXISTS reportInstanceDataAnnotations;
DROP TABLE IF EXISTS reportInstanceEvents;
DROP TABLE IF EXISTS reportInstancePoints;
DROP TABLE IF EXISTS reportInstances;
DROP TABLE IF EXISTS reportInstanceUserComments;
DROP TABLE IF EXISTS reports;
DROP TABLE IF EXISTS scheduledEvents;
DROP TABLE IF EXISTS scripts;
DROP TABLE IF EXISTS systemSettings;
DROP TABLE IF EXISTS userComments;
DROP TABLE IF EXISTS userEvents;
DROP TABLE IF EXISTS users;
DROP TABLE IF EXISTS watchListPoints;
DROP TABLE IF EXISTS watchLists;
DROP TABLE IF EXISTS watchListUsers;
DROP TABLE IF EXISTS eventDetectorTemplates;
DROP TABLE IF EXISTS templatesDetectors;
DROP TABLE IF EXISTS usersProfiles;
DROP TABLE IF EXISTS dataSourceUsersProfiles;
DROP TABLE IF EXISTS dataPointUsersProfiles;
DROP TABLE IF EXISTS usersUsersProfiles;
DROP TABLE IF EXISTS viewUsersProfiles;
DROP TABLE IF EXISTS watchlistUsersProfiles;
DROP TABLE IF EXISTS watchListUsersProfiles;
SET FOREIGN_KEY_CHECKS = 1;